#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装Markdown处理相关依赖包
"""

import subprocess
import sys

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装 {package} 失败: {e}")
        return False

def main():
    """安装所需的包"""
    packages = [
        "html2text",      # HTML到文本转换
        "markdown",       # Markdown到HTML转换
        "markdownify",    # 备选方案
    ]
    
    print("开始安装Markdown处理相关依赖包...")
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count > 0:
        print("\n可以开始使用Markdown转换功能了！")
    else:
        print("\n所有包安装失败，将使用简单的正则表达式处理方案")

if __name__ == "__main__":
    main()
