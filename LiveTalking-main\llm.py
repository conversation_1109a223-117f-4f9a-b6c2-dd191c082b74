import time
import os
import re
import markdown
from bs4 import BeautifulSoup
from basereal import BaseReal
from logger import logger

def markdown_to_text(markdown_string):
    """
    将 Markdown 字符串转换为纯文本。
    保留段落、列表等的基本结构。
    """
    # 将 Markdown 转换为 HTML
    html = markdown.markdown(markdown_string)
    
    # 使用 BeautifulSoup 提取文本
    soup = BeautifulSoup(html, "html.parser")
    
    # 获取纯文本
    text = soup.get_text()
    
    # 可选：处理换行，使文本更整洁
    # 将多个连续换行符减少为最多两个
    lines = (line.strip() for line in text.splitlines())
    text = '\n'.join(line for line in lines if line)
    
    return text



def llm_response(message,nerfreal:BaseReal):
    start = time.perf_counter()
    from openai import OpenAI
    client = OpenAI(
        # 如果您没有配置环境变量，请在此处用您的API Key进行替换
        api_key='sk-d6780468284845ecb2f527aa2c3bbc4c',
        # 填写DashScope SDK的base_url
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )
    end = time.perf_counter()
    logger.info(f"llm Time init: {end-start}s")
    completion = client.chat.completions.create(
        model="qwen-plus",
        messages=[{'role': 'system', 'content': 'You are a helpful assistant.'},
                  {'role': 'user', 'content': message}],
        stream=True,
        # 通过以下设置，在流式输出的最后一行展示token使用信息
        stream_options={"include_usage": True}
    )
    result=""
    first = True
    for chunk in completion:
        if len(chunk.choices)>0:
            #print(chunk.choices[0].delta.content)
            if first:
                end = time.perf_counter()
                logger.info(f"llm Time to first chunk: {end-start}s")
                first = False
            msg = chunk.choices[0].delta.content
            lastpos=0
            #msglist = re.split('[,.!;:，。！?]',msg)
            for i, char in enumerate(msg):
                if char in ",.!;:，。！？：；" :
                    result = result+msg[lastpos:i+1]
                    lastpos = i+1
                    if len(result)>10:
                        # 转换Markdown为纯文本
                        plain_text = markdown_to_text(result)
                        if plain_text.strip():  # 只有非空文本才发送
                            nerfreal.put_msg_txt(plain_text)
                        result=""
            result = result+msg[lastpos:]
    end = time.perf_counter()
    logger.info(f"llm Time to last chunk: {end-start}s")

    # 处理最后剩余的文本
    if result.strip():
        plain_text = markdown_to_text(result)
        if plain_text.strip():
            nerfreal.put_msg_txt(plain_text)

