import time
import os
import re
from basereal import BaseReal
from logger import logger

def markdown_to_speech_text(text):
    """
    将Markdown格式的文本转换为适合语音合成的纯文本
    """
    if not text:
        return ""

    # 移除emoji表情符号
    emoji_pattern = re.compile("["
        u"\U0001F600-\U0001F64F"  # emoticons
        u"\U0001F300-\U0001F5FF"  # symbols & pictographs
        u"\U0001F680-\U0001F6FF"  # transport & map symbols
        u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
        u"\U00002500-\U00002BEF"  # chinese char
        u"\U00002702-\U000027B0"
        u"\U00002702-\U000027B0"
        u"\U000024C2-\U0001F251"
        u"\U0001f926-\U0001f937"
        u"\U00010000-\U0010ffff"
        u"\u2640-\u2642"
        u"\u2600-\u2B55"
        u"\u200d"
        u"\u23cf"
        u"\u23e9"
        u"\u231a"
        u"\ufe0f"  # dingbats
        u"\u3030"
        "]+", flags=re.UNICODE)

    text = emoji_pattern.sub('', text)

    # 移除Markdown语法
    # 移除标题标记 (### 标题 -> 标题)
    text = re.sub(r'^#{1,6}\s*', '', text, flags=re.MULTILINE)

    # 移除粗体和斜体标记 (**text** -> text, *text* -> text)
    text = re.sub(r'\*{1,2}([^*]+)\*{1,2}', r'\1', text)

    # 移除删除线 (~~text~~ -> text)
    text = re.sub(r'~~([^~]+)~~', r'\1', text)

    # 移除代码块标记 (```code``` -> code)
    text = re.sub(r'```[^`]*```', '', text, flags=re.DOTALL)
    text = re.sub(r'`([^`]+)`', r'\1', text)

    # 移除链接 ([text](url) -> text)
    text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)

    # 移除图片 (![alt](url) -> )
    text = re.sub(r'!\[[^\]]*\]\([^)]+\)', '', text)

    # 移除列表标记 (- item -> item, * item -> item, 1. item -> item)
    text = re.sub(r'^[\s]*[-*+]\s+', '', text, flags=re.MULTILINE)
    text = re.sub(r'^[\s]*\d+\.\s+', '', text, flags=re.MULTILINE)

    # 移除引用标记 (> text -> text)
    text = re.sub(r'^>\s*', '', text, flags=re.MULTILINE)

    # 移除水平分割线
    text = re.sub(r'^[-*_]{3,}$', '', text, flags=re.MULTILINE)
    text = re.sub(r'^---+$', '', text, flags=re.MULTILINE)

    # 移除表格分隔符
    text = re.sub(r'\|', ' ', text)

    # 清理多余的空白字符
    text = re.sub(r'\n\s*\n', '\n', text)  # 多个换行变成单个
    text = re.sub(r'[ \t]+', ' ', text)    # 多个空格变成单个
    text = text.strip()

    # 将换行符替换为句号，使语音更自然
    text = re.sub(r'\n+', '。', text)

    return text

def llm_response(message,nerfreal:BaseReal):
    start = time.perf_counter()
    from openai import OpenAI
    client = OpenAI(
        # 如果您没有配置环境变量，请在此处用您的API Key进行替换
        api_key='sk-d6780468284845ecb2f527aa2c3bbc4c',
        # 填写DashScope SDK的base_url
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )
    end = time.perf_counter()
    logger.info(f"llm Time init: {end-start}s")
    completion = client.chat.completions.create(
        model="qwen-plus",
        messages=[{'role': 'system', 'content': 'You are a helpful assistant.'},
                  {'role': 'user', 'content': message}],
        stream=True,
        # 通过以下设置，在流式输出的最后一行展示token使用信息
        stream_options={"include_usage": True}
    )
    result=""
    first = True
    for chunk in completion:
        if len(chunk.choices)>0:
            #print(chunk.choices[0].delta.content)
            if first:
                end = time.perf_counter()
                logger.info(f"llm Time to first chunk: {end-start}s")
                first = False
            msg = chunk.choices[0].delta.content
            lastpos=0
            #msglist = re.split('[,.!;:，。！?]',msg)
            for i, char in enumerate(msg):
                if char in ",.!;:，。！？：；" :
                    result = result+msg[lastpos:i+1]
                    lastpos = i+1
                    if len(result)>10:
                        # 转换Markdown为语音文本
                        speech_text = markdown_to_speech_text(result)
                        if speech_text.strip():  # 只有非空文本才发送
                            logger.info(f"原始文本: {result}")
                            logger.info(f"语音文本: {speech_text}")
                            nerfreal.put_msg_txt(speech_text)
                        result=""
            result = result+msg[lastpos:]
    end = time.perf_counter()
    logger.info(f"llm Time to last chunk: {end-start}s")
    # 处理最后剩余的文本
    if result.strip():
        speech_text = markdown_to_speech_text(result)
        if speech_text.strip():
            logger.info(f"最终原始文本: {result}")
            logger.info(f"最终语音文本: {speech_text}")
            nerfreal.put_msg_txt(speech_text)