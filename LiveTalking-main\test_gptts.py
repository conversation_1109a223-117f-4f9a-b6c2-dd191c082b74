#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 GpTTTS 类的简单脚本
"""

import sys
import os
import time
import argparse
from unittest.mock import Mock

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ttsreal import GpTTTS
from logger import logger

class MockOpt:
    """模拟配置对象"""
    def __init__(self):
        self.fps = 50  # 20ms per frame
        self.REF_FILE = "Keira"  # 音色名称
        self.SPEED_FACTOR = 1.0
        self.TOP_K = 5
        self.TOP_P = 1.0
        self.TEMPERATURE = 1.0

class MockParent:
    """模拟父对象"""
    def __init__(self):
        self.sessionid = "test_session"
        self.audio_frames = []
    
    def put_audio_frame(self, frame, eventpoint=None):
        """接收音频帧"""
        self.audio_frames.append((frame, eventpoint))
        if eventpoint:
            print(f"收到音频帧事件: {eventpoint}")

def test_gptts():
    """测试 GpTTTS 类"""
    print("开始测试 GpTTTS 类...")
    
    # 创建模拟对象
    opt = MockOpt()
    parent = MockParent()
    
    # 创建 GpTTTS 实例
    tts = GpTTTS(opt, parent)
    
    # 测试文本
    test_text = "Live2D是一种能让2D角色以3D方式动起来的技术"
    
    print(f"测试文本: {test_text}")
    print(f"API URL: {tts.api_url}")
    print(f"音色名称: {tts.exp_name}")
    print(f"语速: {tts.speed_factor}")
    print(f"Top-K: {tts.top_k}")
    print(f"Top-P: {tts.top_p}")
    print(f"Temperature: {tts.temperature}")
    
    # 测试语音合成
    try:
        print("\n开始调用语音合成API...")
        start_time = time.time()
        
        # 调用txt_to_audio方法
        tts.txt_to_audio((test_text, None))
        
        end_time = time.time()
        print(f"语音合成完成，耗时: {end_time - start_time:.2f}秒")
        
        # 检查结果
        if parent.audio_frames:
            print(f"成功生成 {len(parent.audio_frames)} 个音频帧")
            
            # 显示事件信息
            for i, (frame, event) in enumerate(parent.audio_frames):
                if event:
                    print(f"帧 {i}: {event}")
        else:
            print("未生成音频帧")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试 GpTTTS 类')
    parser.add_argument('--text', type=str, default="Live2D是一种能让2D角色以3D方式动起来的技术", 
                       help='要合成的测试文本')
    parser.add_argument('--voice', type=str, default="Keira", 
                       help='音色名称')
    
    args = parser.parse_args()
    
    # 更新配置
    MockOpt.REF_FILE = args.voice
    
    # 运行测试
    test_gptts()

if __name__ == "__main__":
    main()
