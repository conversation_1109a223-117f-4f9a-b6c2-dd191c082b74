#!/usr/bin/env python3
"""
使用静态图像模式启动LiveTalking的示例脚本
"""

import subprocess
import sys
import os

def run_with_static_image(image_path, model='musetalk', avatar_id='avator_1'):
    """
    使用静态图像模式启动LiveTalking
    
    Args:
        image_path: 静态图像文件路径
        model: 使用的模型 (musetalk, wav2lip, ultralight)
        avatar_id: avatar ID
    """
    
    if not os.path.exists(image_path):
        print(f"错误: 图像文件不存在: {image_path}")
        return
    
    # 构建启动命令
    cmd = [
        sys.executable, 'app.py',
        '--model', model,
        '--avatar_id', avatar_id,
        '--static_image_mode',
        '--static_image_path', image_path,
        '--tts', 'edgetts',  # 可以根据需要修改
        '--transport', 'webrtc',
        '--listenport', '8010'
    ]
    
    print("启动命令:")
    print(' '.join(cmd))
    print("\n正在启动LiveTalking...")
    print(f"静态图像模式: {image_path}")
    print("访问地址: http://localhost:8010/dashboard.html")
    
    # 启动服务
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"启动失败: {e}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='使用静态图像模式启动LiveTalking')
    parser.add_argument('--image', type=str, required=True, help='静态图像文件路径')
    parser.add_argument('--model', type=str, default='musetalk', 
                       choices=['musetalk', 'wav2lip', 'ultralight'], 
                       help='使用的模型')
    parser.add_argument('--avatar_id', type=str, default='avator_1', help='Avatar ID')
    
    args = parser.parse_args()
    
    run_with_static_image(args.image, args.model, args.avatar_id)

if __name__ == "__main__":
    main()
