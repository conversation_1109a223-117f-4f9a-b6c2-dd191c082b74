#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Markdown到语音文本转换功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from llm import markdown_to_speech_text

def test_markdown_conversion():
    """测试各种Markdown格式的转换"""
    
    test_cases = [
        {
            "name": "基本Markdown语法",
            "input": """
### 🍺 成都美食文化特色：

- **重口味**：麻辣为主，讲究"麻、辣、鲜、香、烫"。
- **小吃丰富**：街头巷尾都能找到美味。
- **夜生活丰富**：成都人喜欢夜宵、宵夜文化浓厚。

✅ 多尝试本地人推荐的小店
✅ 不要只吃网红店，有些苍蝇馆子味道更地道
✅ 带好胃和一颗不怕辣的心 😄
            """,
            "expected_contains": ["成都美食文化特色", "重口味", "麻辣为主", "小吃丰富", "夜生活丰富"]
        },
        {
            "name": "复杂Markdown",
            "input": """
## 🌶️ 经典川菜 & 成都小吃：

1. **火锅（Hot Pot）**  
   - 推荐：小龙坎、蜀九香、大龙燚、冒椒火辣等。

2. **串串香（Chuan Chuan Xiang）**  
   - 类似火锅，但菜品串在竹签上，自己拿签签，

> 建议：多尝试本地人推荐的小店

---

需要我帮你推荐具体餐厅或路线吗？
            """,
            "expected_contains": ["经典川菜", "成都小吃", "火锅", "串串香", "建议", "推荐具体餐厅"]
        },
        {
            "name": "纯emoji和符号",
            "input": "🍺✅🌶️😄🔥💯",
            "expected_contains": []
        },
        {
            "name": "代码块",
            "input": """
这是一段文本
```python
def hello():
    print("world")
```
这是另一段文本
            """,
            "expected_contains": ["这是一段文本", "这是另一段文本"]
        }
    ]
    
    print("开始测试Markdown转换功能...\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_case['name']}")
        print("=" * 50)
        
        input_text = test_case["input"]
        output_text = markdown_to_speech_text(input_text)
        
        print(f"输入文本:\n{input_text}")
        print(f"\n输出文本:\n{output_text}")
        
        # 检查期望内容
        if test_case["expected_contains"]:
            missing = []
            for expected in test_case["expected_contains"]:
                if expected not in output_text:
                    missing.append(expected)
            
            if missing:
                print(f"\n❌ 测试失败: 缺少期望内容: {missing}")
            else:
                print(f"\n✅ 测试通过: 包含所有期望内容")
        else:
            if output_text.strip():
                print(f"\n❌ 测试失败: 期望空输出，但得到: '{output_text}'")
            else:
                print(f"\n✅ 测试通过: 正确移除了所有emoji")
        
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    test_markdown_conversion()
