import markdown
from bs4 import BeautifulSoup

def markdown_to_text(markdown_string):
    """
    将 Markdown 字符串转换为纯文本。
    保留段落、列表等的基本结构。
    """
    # 将 Markdown 转换为 HTML
    html = markdown.markdown(markdown_string)
    
    # 使用 BeautifulSoup 提取文本
    soup = BeautifulSoup(html, "html.parser")
    
    # 获取纯文本
    text = soup.get_text()
    
    # 可选：处理换行，使文本更整洁
    # 将多个连续换行符减少为最多两个
    lines = (line.strip() for line in text.splitlines())
    text = '\n'.join(line for line in lines if line)
    
    return text

# 示例用法
markdown_input = """
# 这是一个标题

这是一个**加粗**的段落，还有一个[链接](https://example.com)。

## 二级标题

- 列表项 1
- 列表项 2
- 列表项 3

`这是一行代码`
"""

plain_text = markdown_to_text(markdown_input)
print(plain_text)